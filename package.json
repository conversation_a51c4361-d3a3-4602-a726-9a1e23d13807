{"name": "coozf-open-trpc", "private": true, "scripts": {"build:client": "pnpm -F client build", "build:server": "pnpm -F server build", "build:copy-frontend": "pnpm run build:client && mkdir -p apps/server/public && cp -r apps/client/dist/* apps/server/public/", "build": "run-s build:client build:server", "dev:client": "pnpm -F client dev", "dev:server": "pnpm -F server dev", "dev:admin": "pnpm -F admin dev", "dev": "run-p dev:*", "push": "pnpm -F server push", "studio": "pnpm -F server studio", "start:client": "pnpm -F client start", "start:server": "pnpm -F server start", "start": "run-p start:*", "pre": "pnpm -F pres-e2e pre", "type-check:server": "pnpm -F server type-check", "type-check:client": "pnpm -F client type-check", "type-check": "run-p type-check:*", "clean": "find . -name 'node_modules' -type d -prune -exec rm -rf '{}' + && find . -name 'package-lock.json' -type f -delete && find . -name 'yarn.lock ' -type f -delete && find . -name 'dist' -type d -prune -exec rm -rf '{}' +", "prepare": "husky"}, "devDependencies": {"@eslint/js": "^9.25.0", "@total-typescript/tsconfig": "^1.0.4", "@types/node": "^24.0.3", "eslint": "^9.26.0", "husky": "^9.1.7", "npm-run-all": "^4.1.5", "tsx": "^4.20.3", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "wait-port": "^1.1.0"}, "dependencies": {"superjson": "^2.2.2"}}