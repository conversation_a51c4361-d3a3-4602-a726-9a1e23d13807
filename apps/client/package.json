{"name": "@coozf/client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode dev", "build": "tsc --noEmit && vite build", "lint": "eslint .", "type-check": "tsc --noEmit", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-form": "^0.1.7", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.10", "@tanstack/react-query": "^5.0.0", "@tanstack/react-router": "^1.121.34", "@tanstack/router-devtools": "^1.121.34", "@trpc/client": "^11.4.2", "@trpc/react-query": "^11.4.2", "@trpc/tanstack-react-query": "^11.4.2", "better-auth": "^1.2.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.518.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "recharts": "^2.15.1", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "zod": "^3.25.67"}, "devDependencies": {"@tanstack/router-cli": "^1.121.34", "@tanstack/router-plugin": "^1.121.34", "@types/node": "^24.0.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react-swc": "^3.9.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "tw-animate-css": "^1.3.4", "vite": "^6.3.5"}}