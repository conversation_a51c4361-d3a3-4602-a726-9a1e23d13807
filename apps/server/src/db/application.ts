import { relations } from 'drizzle-orm'
import { pgTable, index } from 'drizzle-orm/pg-core'
import { createInsertSchema, createSelectSchema } from 'drizzle-zod'
import { z } from 'zod'
import { timestamps } from './columns.helpers'
import { users } from './user'

// 应用表
export const applications = pgTable(
  'applications',
  (t) => ({
    id: t.uuid().notNull().primaryKey().defaultRandom(),
    userId: t
      .uuid()
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    appId: t.varchar({ length: 64 }).notNull().unique(), // 公开的应用ID
    name: t.varchar({ length: 100 }).notNull(),
    description: t.text(),
    secret: t.varchar({ length: 255 }).notNull(), // 加密存储的应用密钥
    status: t.varchar({ length: 20 }).notNull().default('active'), // active, suspended, deleted
    webhookUrl: t.varchar({ length: 500 }), // 应用的webhook回调地址
    ...timestamps,
  }),
  (table) => [
    index('applications_user_id_idx').on(table.userId),
    index('applications_app_id_idx').on(table.appId),
    index('applications_status_idx').on(table.status),
  ]
)

// 应用与用户的关系
export const applicationsRelations = relations(applications, ({ one }) => ({
  user: one(users, {
    fields: [applications.userId],
    references: [users.id],
  }),
}))

// 类型推导
export type Application = typeof applications.$inferSelect
export type InsertApplication = typeof applications.$inferInsert

// 应用状态枚举
export const ApplicationStatus = {
  ACTIVE: 'active',
  SUSPENDED: 'suspended',
  DELETED: 'deleted',
} as const

export type ApplicationStatusType = (typeof ApplicationStatus)[keyof typeof ApplicationStatus]

// 应用相关的 Zod Schema
export const CreateApplicationSchema = createInsertSchema(applications, {
  name: z.string().min(1, '应用名称不能为空').max(100, '应用名称不能超过100个字符'),
  description: z.string().max(500, '应用描述不能超过500个字符').optional(),
  webhookUrl: z.string().url('Webhook URL格式错误').optional(),
}).omit({
  id: true,
  userId: true,
  appId: true,
  secret: true,
  status: true,
  createdAt: true,
  updatedAt: true,
})

export const UpdateApplicationSchema = createInsertSchema(applications, {
  name: z.string().min(1, '应用名称不能为空').max(100, '应用名称不能超过100个字符').optional(),
  description: z.string().max(500, '应用描述不能超过500个字符').optional(),
  webhookUrl: z.string().url('Webhook URL格式错误').optional(),
  status: z
    .enum(['active', 'suspended'], {
      errorMap: () => ({ message: '无效的应用状态' }),
    })
    .optional(),
}).omit({
  id: true,
  userId: true,
  appId: true,
  secret: true,
  createdAt: true,
  updatedAt: true,
})

export const SelectApplicationSchema = createSelectSchema(applications)

// 应用列表查询参数
export const ApplicationListSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
  search: z.string().optional(),
})

export type ApplicationListParams = z.infer<typeof ApplicationListSchema>

// 扩展应用类型，包含余额信息
export type ApplicationWithBalance = Application & {
  balance: string
}
