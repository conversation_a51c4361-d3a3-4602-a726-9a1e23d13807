import { pgTable, index } from 'drizzle-orm/pg-core'
import { createInsertSchema, createSelectSchema } from 'drizzle-zod'
import { z } from 'zod'
import { timestamps } from './columns.helpers'
import { users } from './user'
import { applications } from './application'

// 订单表
export const orders = pgTable('orders', (t) => ({
  id: t.uuid().notNull().primaryKey().defaultRandom(),
  orderNo: t.varchar({ length: 32 }).notNull().unique(), // 订单号
  userId: t.uuid().notNull().references(() => users.id, { onDelete: 'cascade' }),
  applicationId: t.uuid().notNull().references(() => applications.id, { onDelete: 'cascade' }), // 关联应用ID（必选，充值必须选应用）
  
  // 产品信息
  antCoins: t.numeric({ precision: 10, scale: 2 }).notNull(), // 蚁贝数量
  amount: t.numeric({ precision: 10, scale: 2 }).notNull(), // 对应金额
  
  // 订单分类
  source: t.varchar({ length: 20 }).default('SYSTEM').notNull(), // 来源：SYSTEM(系统订单)
  type: t.varchar({ length: 20 }).notNull(), // 类型：PURCHASE(购买), GIFT(赠送)
  paymentMethod: t.varchar({ length: 50 }).default('BANK_TRANSFER').notNull(), // 付款方式：BANK_TRANSFER(对公转账)
  
  // 状态管理
  status: t.varchar({ length: 20 }).default('COMPLETED').notNull(), // 状态：PENDING(待付款), COMPLETED(已完成), CANCELLED(已取消)
  
  // 发票申请
  invoiceRequested: t.boolean().default(false).notNull(), // 发票申请标识
  
  // 备注
  remarks: t.varchar({ length: 500 }), // 备注说明
  
  ...timestamps,
}), (table) => ({
  orderNoIdx: index('orders_order_no_idx').on(table.orderNo),
  userIdIdx: index('orders_user_id_idx').on(table.userId),
  statusIdx: index('orders_status_idx').on(table.status),
  typeIdx: index('orders_type_idx').on(table.type),
  createdAtIdx: index('orders_created_at_idx').on(table.createdAt),
}))

// 类型推导
export type Order = typeof orders.$inferSelect
export type InsertOrder = typeof orders.$inferInsert

// 订单来源枚举
export const OrderSource = {
  SYSTEM: 'SYSTEM', // 系统订单
} as const

export type OrderSourceEnum = typeof OrderSource[keyof typeof OrderSource]

// 订单类型枚举
export const OrderType = {
  PURCHASE: 'PURCHASE', // 购买
  GIFT: 'GIFT',         // 赠送
} as const

export type OrderTypeEnum = typeof OrderType[keyof typeof OrderType]

// 付款方式枚举
export const PaymentMethod = {
  BANK_TRANSFER: 'BANK_TRANSFER', // 对公转账
} as const

export type PaymentMethodEnum = typeof PaymentMethod[keyof typeof PaymentMethod]

// 订单状态枚举
export const OrderStatus = {
  PENDING: 'PENDING',     // 待付款
  COMPLETED: 'COMPLETED', // 已完成
  CANCELLED: 'CANCELLED', // 已取消
} as const

export type OrderStatusEnum = typeof OrderStatus[keyof typeof OrderStatus]

// Zod Schema
export const CreateOrderSchema = createInsertSchema(orders, {
  antCoins: z.number().min(0.01, '蚁贝数量必须大于0'),
  amount: z.number().min(0.01, '金额必须大于0'),
  type: z.enum(['PURCHASE', 'GIFT']),
  source: z.enum(['SYSTEM']).default('SYSTEM'),
  paymentMethod: z.enum(['BANK_TRANSFER']).default('BANK_TRANSFER'),
  status: z.enum(['PENDING', 'COMPLETED', 'CANCELLED']).default('COMPLETED'),
  remarks: z.string().max(500, '备注不能超过500个字符').optional(),
}).omit({
  id: true,
  orderNo: true,
  invoiceRequested: true,
  createdAt: true,
  updatedAt: true,
})

export const UpdateOrderSchema = createInsertSchema(orders, {
  status: z.enum(['PENDING', 'COMPLETED', 'CANCELLED']).optional(),
  invoiceRequested: z.boolean().optional(),
  remarks: z.string().max(500, '备注不能超过500个字符').optional(),
}).omit({
  id: true,
  orderNo: true,
  userId: true,
  applicationId: true,
  antCoins: true,
  amount: true,
  source: true,
  type: true,
  paymentMethod: true,
  createdAt: true,
  updatedAt: true,
})

export const SelectOrderSchema = createSelectSchema(orders)

// 订单列表查询参数
export const OrderListSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
  userId: z.string().uuid().optional(),
  status: z.enum(['PENDING', 'COMPLETED', 'CANCELLED']).optional(),
  type: z.enum(['PURCHASE', 'GIFT']).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  search: z.string().optional(), // 搜索订单号
})

export type OrderListParams = z.infer<typeof OrderListSchema>

// 管理员充值Schema
export const AdminRechargeSchema = z.object({
  applicationId: z.string().uuid('请选择应用'), // 必选
  amount: z.number().min(0.01, '充值金额必须大于0'),
  type: z.enum(['PURCHASE', 'GIFT']).default('GIFT'),
  remarks: z.string().max(500, '备注不能超过500个字符').optional(),
})

export type AdminRechargeParams = z.infer<typeof AdminRechargeSchema> 