import { drizzle } from 'drizzle-orm/node-postgres'
import * as schema from './schema'
import { env } from '../env'
import { Pool } from 'pg'
const pool = new Pool({
  connectionString: env.DATABASE_URL,
  options: `-c timezone=${env.DEFAULT_TIMEZONE}`, // 这是关键配置
})

// 测试数据库连接
pool
  .connect()
  .then(() => {
    console.log('✅ PostgreSQL 数据库连接成功')
  })
  .catch((error) => {
    console.error('❌ PostgreSQL 数据库连接失败:', error)
    process.exit(1) // 如果数据库连接失败，终止程序
  })

export const db = drizzle(pool, {
  schema,
  casing: 'snake_case',
})
