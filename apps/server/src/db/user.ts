import { pgTable, index } from 'drizzle-orm/pg-core'
import { createInsertSchema, createSelectSchema } from 'drizzle-zod'
import z from 'zod'
import { timestamps } from './columns.helpers'

// 用户表
export const users = pgTable('users', (t) => ({
  id: t.uuid().notNull().primaryKey().defaultRandom(),
  email: t.varchar({ length: 255 }).unique(),
  phone: t.varchar({ length: 20 }).unique(),
  password: t.varchar({ length: 255 }),
  name: t.varchar({ length: 100 }),
  phoneVerified: t.boolean().default(false).notNull(),
  emailVerified: t.boolean().default(false).notNull(),
  avatar: t.varchar({ length: 500 }),
  ...timestamps,
}))

// 用户会话表
export const userSessions = pgTable(
  'user_sessions',
  (t) => ({
    id: t.uuid().notNull().primaryKey().defaultRandom(),
    userId: t
      .uuid()
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    deviceId: t.varchar({ length: 64 }).notNull(), // 设备唯一标识
    refreshToken: t.varchar({ length: 500 }).notNull(), // 加密存储的 refresh token
    accessTokenJti: t.varchar({ length: 64 }), // 当前 access token 的 JTI
    expiresAt: t.timestamp().notNull(), // refresh token 过期时间
    lastUsedAt: t.timestamp().notNull().defaultNow(), // 最后使用时间
    isActive: t.boolean().default(true).notNull(), // 会话是否活跃
    deviceInfo: t.jsonb().$type<{
      userAgent?: string
      ip?: string
      platform?: string
      browser?: string
      os?: string
      [key: string]: unknown
    }>(), // 设备信息
    ...timestamps,
  }),
  (table) => [
    index('user_sessions_user_id_idx').on(table.userId),
    index('user_sessions_device_id_idx').on(table.deviceId),
    index('user_sessions_refresh_token_idx').on(table.refreshToken),
    index('user_sessions_access_token_jti_idx').on(table.accessTokenJti),
    index('user_sessions_expires_at_idx').on(table.expiresAt),
    index('user_sessions_is_active_idx').on(table.isActive),
  ]
)

// 类型推导
export type User = typeof users.$inferSelect
export type InsertUser = typeof users.$inferInsert
export type UserSession = typeof userSessions.$inferSelect
export type InsertUserSession = typeof userSessions.$inferInsert

// 用户相关的 Zod Schema
export const CreateUserSchema = createInsertSchema(users, {
  email: z.string().email('请输入有效的邮箱地址').optional(),
  phone: z
    .string()
    .regex(/^1[3-9]\d{9}$/, '请输入有效的手机号')
    .optional(),
  password: z.string().min(6, '密码至少6位').optional(),
  name: z.string().min(1, '姓名不能为空').optional(),
}).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  phoneVerified: true,
  emailVerified: true,
})

export const LoginSchema = z
  .object({
    email: z.string().email('请输入有效的邮箱地址').optional(),
    phone: z
      .string()
      .regex(/^1[3-9]\d{9}$/, '请输入有效的手机号')
      .optional(),
    password: z.string().min(6, '密码至少6位'),
  })
  .refine((data) => data.email ?? data.phone, {
    message: '请输入邮箱或手机号',
  })

export const SelectUserSchema = createSelectSchema(users).omit({
  password: true, // 不返回密码
})

// 用户会话相关的 Zod Schema
export const CreateUserSessionSchema = createInsertSchema(userSessions, {
  userId: z.string().uuid('用户ID格式错误'),
  deviceId: z.string().min(1, '设备ID不能为空'),
  refreshToken: z.string().min(1, 'Refresh Token不能为空'),
  expiresAt: z.date(),
}).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
})

export const SelectUserSessionSchema = createSelectSchema(userSessions)
