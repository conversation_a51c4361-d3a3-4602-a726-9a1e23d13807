// src/env.ts
import { z } from 'zod'
import dotenv from 'dotenv'
import { resolve } from 'path'

const mode = process.env.mode || 'dev'

// 按优先级加载配置文件
const envFiles = [`.env.${mode}.local`, `.env.local`, `.env.${mode}`, '.env']

// 加载环境变量文件
envFiles.forEach((file) => {
  dotenv.config({
    path: resolve(process.cwd(), file),
    override: false, // 不覆盖已存在的环境变量
  })
})

const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production']),
  MODE: z.enum(['dev', 'prod', 'pre']).default('dev'),
  PORT: z.string().transform(Number).default('3000'),
  SERVER_BASE_URL: z.string().url().default('http://localhost:3000'),
  DATABASE_URL: z.string(),
  JWT_SECRET: z.string().min(32, 'JWT密钥至少需要32个字符'),
  DEFAULT_TIMEZONE: z.string().default('Asia/Shanghai'),
  REDIS_HOST: z.string().default('localhost'),
  REDIS_PORT: z.string().transform(Number).default('6379'),
  REDIS_PASSWORD: z.string().default(''),
  REDIS_USERNAME: z.string().default('default'),
  REDIS_DB: z.string().transform(Number).default('0'),

  // 日志配置
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),

  // CORS 配置
  CORS_ORIGIN: z.string().optional(),

  // 小红书
  XIAOHONGSHU_CLIENT_SECRET: z.string(),
  XIAOHONGSHU_CLIENT_KEY: z.string(),
  XIAOHONGSHU_SECRET: z.string(),

  // 快代理配置参数
  KUAI_ADDRESS: z.string(),
  KUAI_USERNAME: z.string(),
  KUAI_PASSWORD: z.string(),

  CRAWLER_URL: z.string(),
})

export const env = envSchema.parse(process.env)

// 导出常用的环境判断
export const isdev = env.NODE_ENV === 'development'
export const isprod = env.MODE === 'prod'

// 导出处理后的配置
export const config = {
  ...env,
  corsOrigins: env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
  serverBaseUrl: env.SERVER_BASE_URL,
  redis: {
    host: env.REDIS_HOST,
    port: env.REDIS_PORT,
    password: env.REDIS_PASSWORD || undefined,
    username: env.REDIS_USERNAME,
    db: env.REDIS_DB,
  },
} as const
