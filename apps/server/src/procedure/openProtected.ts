import { db } from '@/db'
import { applications, users } from '@/db/schema'
import type { User, Application } from '@/db/schema'
import { verifyOpenAPIToken } from '@/lib/jwt'
import { t } from '@/trpc'
import { TRPCError } from '@trpc/server'
import { eq } from 'drizzle-orm'
import { LRUCache } from 'lru-cache'

// 缓存用户-应用组合数据
type UserAppData = {
  user: User
  application: Application
}

const userAppCache = new LRUCache<string, UserAppData>({
  max: 1000,
  ttl: 5 * 60 * 1000, // 5分钟缓存
})

// 验证token是否有效
export async function verifyToken(authHeader: string) {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: '请提供有效的 Authorization header',
    })
  }

  const token = authHeader.slice(7)

  try {
    const payload = verifyOpenAPIToken(token)
    const cacheKey = `${payload.userId}:${payload.appId}`

    let userAppData = userAppCache.get(cacheKey)

    if (!userAppData) {
      // JOIN查询确保权限验证
      const result = await db
        .select()
        .from(applications)
        .innerJoin(users, eq(applications.userId, users.id))
        .where(eq(applications.appId, payload.appId))
        .limit(1)

      if (!result[0]) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '应用或用户不存在',
        })
      }

      userAppData = {
        user: result[0].users,
        application: result[0].applications,
      }

      userAppCache.set(cacheKey, userAppData)
    }

    return userAppData
  } catch {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Token 无效或已过期',
    })
  }
}

export const openAPIMiddleware = t.middleware(async ({ ctx, next }) => {
  const authHeader = ctx.req.headers.authorization

  const userAppData = await verifyToken(authHeader ?? '')

  return next({
    ctx: {
      ...ctx,
      user: userAppData.user,
      application: userAppData.application,
    },
  })
})

export const openAPIProcedure = t.procedure.use(openAPIMiddleware)
