import { TRPCError } from '@trpc/server'
import { z } from 'zod'
import { router } from '@/trpc'
import { openAPIProcedure } from '@/procedure'
import { authAccounts, OAuthAuthorizeSchema } from '@/db/schema'
import { generateOAuthState, generateAuthorizeUrl } from '@/lib/oauth2'

export const oauth2Router = router({
  // 生成OAuth授权URL
  generateAuthorizeUrl: openAPIProcedure
    .meta({
      openapi: {
        method: 'POST',
        path: '/oauth2/authorize',
        summary: '生成OAuth授权URL',
        description: '为指定平台生成OAuth授权URL，用户点击后跳转到对应平台授权页面',
        tags: ['OAuth2'],
      },
    })
    .input(OAuthAuthorizeSchema)
    .output(
      z.object({
        authorizeUrl: z.string().url('授权URL'),
        state: z.string().describe('状态参数'),
        platform: z.string().describe('平台标识'),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { platform } = input

      if (!ctx.application.webhookUrl) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '应用未配置Webhook URL，请先在应用设置中配置',
        })
      }

      // 生成回调URL（指向我们的服务器）
      const redirectUri = `https://1c3e-118-249-196-233.ngrok-free.app/api/oauth2/${platform}/callback`

      // 生成带有应用和平台信息的状态参数
      const state = generateOAuthState(ctx.application.id, platform)

      // 生成授权URL
      const authorizeUrl = generateAuthorizeUrl(platform, redirectUri, state)

      // 临时存储授权信息到数据库（有效期15分钟）
      try {
        await ctx.db.insert(authAccounts).values({
          appId: ctx.application.id,
          platform,
          platformUserId: 'temp_' + state, // 临时占位
          state,
          scope: 'basic_info',
          userInfo: { temp: true, timestamp: Date.now() },
        })
      } catch (error) {
        console.error('存储OAuth临时数据失败:', error)
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '生成授权链接失败',
        })
      }

      return {
        authorizeUrl,
        state,
        platform,
      }
    }),
})
