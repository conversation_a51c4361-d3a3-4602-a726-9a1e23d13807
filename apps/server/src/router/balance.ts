import { z } from 'zod'
import { router } from '@/trpc'
import { applicationProcedure, applicationWithBalanceProcedure } from '@/procedure'
import {
  getApplicationTransactions,
  checkApplicationBalance,
  calculateAccountQuotaCost,
  calculateTrafficCost,
  getApiCalls,
} from '@/lib/balance'
import { TransactionListSchema, ApiCallListSchema } from '@/db/balance'

export const balanceRouter = router({
  // 获取应用蚁贝余额
  getApplicationBalance: applicationWithBalanceProcedure.query(async ({ ctx }) => {
    return { balance: ctx.applicationWithBalance.balance, applicationName: ctx.applicationWithBalance.name }
  }),

  // 获取应用交易记录
  getApplicationTransactions: applicationProcedure.input(TransactionListSchema).query(async ({ ctx, input }) => {
    const { page, pageSize } = input

    const transactions = await getApplicationTransactions(ctx.applicationId, page, pageSize)
    return { data: transactions, page, pageSize }
  }),

  // 检查应用余额是否足够
  checkApplicationBalance: applicationProcedure
    .input(z.object({ amount: z.number().min(0.01, '金额必须大于0') }))
    .query(async ({ input, ctx }) => {
      const sufficient = await checkApplicationBalance(ctx.applicationId, input.amount)
      return { sufficient }
    }),

  // 计算购买账号额度所需蚁贝
  calculateAccountQuotaCost: applicationProcedure
    .input(
      z.object({
        quotaCount: z.number().min(1, '额度数量必须大于0'),
      })
    )
    .query(async ({ input }) => {
      const cost = calculateAccountQuotaCost(input.quotaCount)
      return { cost, quotaCount: input.quotaCount }
    }),

  // 计算购买流量所需蚁贝
  calculateTrafficCost: applicationProcedure
    .input(
      z.object({
        trafficGB: z.number().min(0.1, '流量必须大于0.1GB'),
      })
    )
    .query(async ({ input }) => {
      const cost = calculateTrafficCost(input.trafficGB)
      return { cost, trafficGB: input.trafficGB }
    }),

  // 获取API调用记录
  getApiCalls: applicationProcedure.input(ApiCallListSchema).query(async ({ input, ctx }) => {
    const { page, pageSize, endpoint, costType, startDate, endDate } = input

    const apiCalls = await getApiCalls(ctx.applicationId, page, pageSize, {
      endpoint,
      costType,
      startDate,
      endDate,
    })
    return { data: apiCalls, page, pageSize }
  }),
})