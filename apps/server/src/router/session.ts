import { z } from 'zod'
import { router } from '../trpc'
import { TRPCError } from '@trpc/server'
import { SessionService } from '../lib/session'
import { protectedProcedure } from '@/procedure'

export const sessionRouter = router({
  // 获取用户的所有活跃会话
  getSessions: protectedProcedure.query(async ({ ctx }) => {
    try {
      const sessions = await SessionService.getUserActiveSessions(ctx.user.id)
      
      // 不返回敏感信息
      return sessions.map(session => ({
        id: session.id,
        deviceId: session.deviceId,
        deviceInfo: session.deviceInfo,
        lastUsedAt: session.lastUsedAt,
        createdAt: session.createdAt,
        expiresAt: session.expiresAt,
        isActive: session.isActive,
      }))
    } catch (error) {
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: error instanceof Error ? error.message : '获取会话列表失败',
      })
    }
  }),

  // 强制登出特定会话
  revokeSession: protectedProcedure
    .input(z.object({
      sessionId: z.string().uuid('会话ID格式错误'),
    }))
    .mutation(async ({ input, ctx }) => {
      try {
        // 验证会话是否属于当前用户
        const sessions = await SessionService.getUserActiveSessions(ctx.user.id)
        const targetSession = sessions.find(s => s.id === input.sessionId)
        
        if (!targetSession) {
          throw new Error('会话不存在或不属于当前用户')
        }

        await SessionService.invalidateSession(input.sessionId)

        return {
          message: '会话已撤销',
        }
      } catch (error) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: error instanceof Error ? error.message : '撤销会话失败',
        })
      }
    }),

  // 登出其他所有设备
  revokeOtherSessions: protectedProcedure.mutation(async ({ ctx }) => {
    try {
      // 获取当前会话的 JTI（如果有的话）
      const currentJti = (ctx as any).tokenPayload?.jti

      if (currentJti) {
        // 获取所有活跃会话
        const sessions = await SessionService.getUserActiveSessions(ctx.user.id)
        
        // 找到当前会话并保留，撤销其他会话
        for (const session of sessions) {
          if (session.accessTokenJti !== currentJti) {
            await SessionService.invalidateSession(session.id)
          }
        }
      } else {
        // 如果没有 JTI，撤销所有会话（旧版本兼容）
        await SessionService.invalidateUserSessions(ctx.user.id)
      }

      return {
        message: '已登出其他所有设备',
      }
    } catch (error) {
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: error instanceof Error ? error.message : '登出其他设备失败',
      })
    }
  }),

  // 登出所有设备（包括当前设备）
  revokeAllSessions: protectedProcedure.mutation(async ({ ctx }) => {
    try {
      await SessionService.invalidateUserSessions(ctx.user.id)

      // 清除当前的 cookies
      ctx.res.setCookie('auth-token', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 0,
      })

      ctx.res.setCookie('refresh-token', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 0,
      })

      return {
        message: '已登出所有设备',
      }
    } catch (error) {
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: error instanceof Error ? error.message : '登出所有设备失败',
      })
    }
  }),

  // 清理过期会话（管理员功能）
  cleanupExpiredSessions: protectedProcedure.mutation(async ({ ctx }) => {
    try {
      // 这里可以添加管理员权限检查
      // if (!ctx.user.isAdmin) {
      //   throw new Error('权限不足')
      // }

      const cleanedCount = await SessionService.cleanupExpiredSessions()

      return {
        message: `已清理 ${cleanedCount} 个过期会话`,
        cleanedCount,
      }
    } catch (error) {
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: error instanceof Error ? error.message : '清理过期会话失败',
      })
    }
  }),
})
