import { createHash } from 'crypto'
import UAParser from 'ua-parser-js'

export interface DeviceInfo {
  userAgent?: string
  ip?: string
  platform?: string
  browser?: string
  os?: string
  [key: string]: unknown
}

/**
 * 生成设备指纹
 * 基于 User-Agent 和 IP 地址生成唯一的设备标识
 */
export function generateDeviceId(userAgent: string, ip: string): string {
  const deviceString = `${userAgent}:${ip}`
  return createHash('sha256').update(deviceString).digest('hex').substring(0, 32)
}

/**
 * 解析设备信息
 */
export function parseDeviceInfo(userAgent?: string, ip?: string): DeviceInfo {
  const deviceInfo: DeviceInfo = {
    userAgent,
    ip,
  }

  if (userAgent) {
    const parser = new UAParser(userAgent)
    const result = parser.getResult()

    deviceInfo.platform = result.device.type || 'desktop'
    deviceInfo.browser = result.browser.name
    deviceInfo.os = result.os.name
  }

  return deviceInfo
}

/**
 * 获取客户端 IP 地址
 */
export function getClientIP(request: any): string {
  // 尝试从各种可能的头部获取真实 IP
  const forwarded = request.headers['x-forwarded-for']
  const realIP = request.headers['x-real-ip']
  const remoteAddress = request.connection?.remoteAddress || request.socket?.remoteAddress

  if (forwarded) {
    // x-forwarded-for 可能包含多个 IP，取第一个
    return forwarded.split(',')[0].trim()
  }

  if (realIP) {
    return realIP
  }

  return remoteAddress || 'unknown'
}

/**
 * 获取用户代理字符串
 */
export function getUserAgent(request: any): string {
  return request.headers['user-agent'] || 'unknown'
}
