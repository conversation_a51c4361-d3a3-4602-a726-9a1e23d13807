import { db } from '@/db'
import { userSessions, type UserSession, type InsertUserSession } from '@/db/schema'
import { eq, and, lt, desc } from 'drizzle-orm'
import { generateRefreshToken, verifyRefreshToken, type RefreshTokenPayload } from './jwt'
import { generateDeviceId, parseDeviceInfo, type DeviceInfo } from './device'
import { createHash } from 'crypto'

/**
 * 会话管理服务
 */
export class SessionService {
  /**
   * 创建新会话
   */
  static async createSession(
    userId: string,
    deviceId: string,
    deviceInfo: DeviceInfo,
    expiresInDays: number = 30
  ): Promise<{ session: UserSession; refreshToken: string }> {
    // 计算过期时间
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + expiresInDays)

    // 先检查是否存在同设备的活跃会话，如果存在则使其失效
    await this.invalidateDeviceSessions(userId, deviceId)

    // 创建会话记录（先不包含 refreshToken）
    const sessionData: Omit<InsertUserSession, 'refreshToken'> = {
      userId,
      deviceId,
      deviceInfo,
      expiresAt,
      isActive: true,
      refreshToken: '', // 临时值，稍后更新
    }

    const [session] = await db.insert(userSessions).values(sessionData as InsertUserSession).returning()

    if (!session) {
      throw new Error('创建会话失败')
    }

    // 生成 refresh token
    const refreshToken = generateRefreshToken({
      userId,
      sessionId: session.id,
    })

    // 加密存储 refresh token
    const hashedRefreshToken = this.hashToken(refreshToken)

    // 更新会话记录，存储加密后的 refresh token
    await db
      .update(userSessions)
      .set({ refreshToken: hashedRefreshToken })
      .where(eq(userSessions.id, session.id))

    return {
      session: { ...session, refreshToken: hashedRefreshToken },
      refreshToken,
    }
  }

  /**
   * 验证并刷新会话
   */
  static async refreshSession(refreshToken: string): Promise<{
    session: UserSession
    newRefreshToken: string
    payload: RefreshTokenPayload
  }> {
    // 验证 refresh token
    const payload = verifyRefreshToken(refreshToken)

    // 查找会话
    const session = await db.query.userSessions.findFirst({
      where: eq(userSessions.id, payload.sessionId),
    })

    if (!session) {
      throw new Error('会话不存在')
    }

    // 检查会话是否活跃
    if (!session.isActive) {
      throw new Error('会话已失效')
    }

    // 检查会话是否过期
    if (session.expiresAt < new Date()) {
      await this.invalidateSession(session.id)
      throw new Error('会话已过期')
    }

    // 验证 refresh token 是否匹配
    const hashedToken = this.hashToken(refreshToken)
    if (session.refreshToken !== hashedToken) {
      // Token 不匹配，可能被盗用，使所有会话失效
      await this.invalidateUserSessions(session.userId)
      throw new Error('刷新令牌无效，已撤销所有会话')
    }

    // 生成新的 refresh token（token 轮换）
    const newRefreshToken = generateRefreshToken({
      userId: session.userId,
      sessionId: session.id,
    })

    const newHashedToken = this.hashToken(newRefreshToken)

    // 更新会话
    const [updatedSession] = await db
      .update(userSessions)
      .set({
        refreshToken: newHashedToken,
        lastUsedAt: new Date(),
      })
      .where(eq(userSessions.id, session.id))
      .returning()

    if (!updatedSession) {
      throw new Error('更新会话失败')
    }

    return {
      session: updatedSession,
      newRefreshToken,
      payload,
    }
  }

  /**
   * 验证 Access Token 的 JTI
   */
  static async validateAccessToken(jti: string, userId: string): Promise<boolean> {
    const session = await db.query.userSessions.findFirst({
      where: and(
        eq(userSessions.userId, userId),
        eq(userSessions.accessTokenJti, jti),
        eq(userSessions.isActive, true)
      ),
    })

    return !!session && session.expiresAt > new Date()
  }

  /**
   * 更新会话的 Access Token JTI
   */
  static async updateAccessTokenJTI(sessionId: string, jti: string): Promise<void> {
    await db
      .update(userSessions)
      .set({ accessTokenJti: jti })
      .where(eq(userSessions.id, sessionId))
  }

  /**
   * 使特定会话失效
   */
  static async invalidateSession(sessionId: string): Promise<void> {
    await db
      .update(userSessions)
      .set({ isActive: false })
      .where(eq(userSessions.id, sessionId))
  }

  /**
   * 使用户的所有会话失效
   */
  static async invalidateUserSessions(userId: string): Promise<void> {
    await db
      .update(userSessions)
      .set({ isActive: false })
      .where(eq(userSessions.userId, userId))
  }

  /**
   * 使特定设备的会话失效（单点登录）
   */
  static async invalidateDeviceSessions(userId: string, deviceId: string): Promise<void> {
    await db
      .update(userSessions)
      .set({ isActive: false })
      .where(and(
        eq(userSessions.userId, userId),
        eq(userSessions.deviceId, deviceId)
      ))
  }

  /**
   * 获取用户的活跃会话
   */
  static async getUserActiveSessions(userId: string): Promise<UserSession[]> {
    return await db.query.userSessions.findMany({
      where: and(
        eq(userSessions.userId, userId),
        eq(userSessions.isActive, true)
      ),
      orderBy: [desc(userSessions.lastUsedAt)],
    })
  }

  /**
   * 清理过期会话
   */
  static async cleanupExpiredSessions(): Promise<number> {
    const result = await db
      .update(userSessions)
      .set({ isActive: false })
      .where(lt(userSessions.expiresAt, new Date()))

    return result.rowCount || 0
  }

  /**
   * 哈希 token 用于安全存储
   */
  private static hashToken(token: string): string {
    return createHash('sha256').update(token).digest('hex')
  }
}

/**
 * 创建设备会话的便捷函数
 */
export async function createDeviceSession(
  userId: string,
  userAgent: string,
  ip: string
): Promise<{ session: UserSession; refreshToken: string }> {
  const deviceId = generateDeviceId(userAgent, ip)
  const deviceInfo = parseDeviceInfo(userAgent, ip)

  return SessionService.createSession(userId, deviceId, deviceInfo)
}
