import jwt from 'jsonwebtoken'
import { env } from '../env'
import { randomBytes } from 'crypto'

const JWT_SECRET = env.JWT_SECRET
const ACCESS_TOKEN_EXPIRES_IN = '2h' // Access Token 短期有效
const REFRESH_TOKEN_EXPIRES_IN = '30d' // Refresh Token 长期有效
const OPEN_API_EXPIRES_IN = '30d' // 开放平台 token 有效期更长

const { sign, verify, decode } = jwt

export interface JWTPayload {
  userId: string
  phone: string
  jti?: string // JWT ID，用于 token 撤销
  type?: 'access' | 'refresh' // token 类型
}

export interface RefreshTokenPayload {
  userId: string
  sessionId: string
  type: 'refresh'
  jti: string
}

export interface OpenAPIJWTPayload {
  userId: string
  appId: string
  type: 'open_api' // 标识这是开放平台的 token
}

/**
 * 生成 JTI (JWT ID)
 */
export function generateJTI(): string {
  return randomBytes(16).toString('hex')
}

/**
 * 生成 Access Token
 */
export function generateAccessToken(payload: Omit<JWTPayload, 'jti' | 'type'>): string {
  const jti = generateJTI()
  const tokenPayload: JWTPayload = {
    ...payload,
    jti,
    type: 'access',
  }

  return sign(tokenPayload, JWT_SECRET, {
    expiresIn: ACCESS_TOKEN_EXPIRES_IN,
  })
}

/**
 * 生成 Refresh Token
 */
export function generateRefreshToken(payload: Omit<RefreshTokenPayload, 'jti' | 'type'>): string {
  const jti = generateJTI()
  const tokenPayload: RefreshTokenPayload = {
    ...payload,
    jti,
    type: 'refresh',
  }

  return sign(tokenPayload, JWT_SECRET, {
    expiresIn: REFRESH_TOKEN_EXPIRES_IN,
  })
}

/**
 * 生成JWT token (向后兼容)
 * @deprecated 使用 generateAccessToken 替代
 */
export function generateToken(payload: JWTPayload): string {
  console.log('JWT_SECRET', JWT_SECRET)
  return sign(payload, JWT_SECRET, {
    expiresIn: ACCESS_TOKEN_EXPIRES_IN,
  })
}

/**
 * 生成开放平台 API token
 */
export function generateOpenAPIToken(payload: OpenAPIJWTPayload): string {
  return sign(payload, JWT_SECRET, {
    expiresIn: OPEN_API_EXPIRES_IN,
  })
}

/**
 * 验证 Access Token
 */
export function verifyAccessToken(token: string): JWTPayload {
  try {
    const payload = verify(token, JWT_SECRET) as JWTPayload
    if (payload.type && payload.type !== 'access') {
      throw new Error('不是有效的访问令牌')
    }
    return payload
  } catch {
    throw new Error('无效的访问令牌')
  }
}

/**
 * 验证 Refresh Token
 */
export function verifyRefreshToken(token: string): RefreshTokenPayload {
  try {
    const payload = verify(token, JWT_SECRET) as RefreshTokenPayload
    if (!payload || payload.type !== 'refresh') {
      throw new Error('不是有效的刷新令牌')
    }
    return payload
  } catch {
    throw new Error('无效的刷新令牌')
  }
}

/**
 * 验证JWT token (向后兼容)
 * @deprecated 使用 verifyAccessToken 替代
 */
export function verifyToken(token: string): JWTPayload {
  try {
    return verify(token, JWT_SECRET) as JWTPayload
  } catch {
    throw new Error('无效的令牌')
  }
}

/**
 * 验证开放平台 API token
 */
export function verifyOpenAPIToken(token: string): OpenAPIJWTPayload {
  try {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const payload = verify(token, JWT_SECRET) as any
    if (!payload || payload.type !== 'open_api') {
      throw new Error('不是有效的开放平台令牌')
    }
    return payload as OpenAPIJWTPayload
  } catch {
    throw new Error('无效的开放平台令牌')
  }
}

/**
 * 解码JWT token（不验证签名）
 */
export function decodeToken(token: string): JWTPayload | null {
  try {
    return decode(token) as JWTPayload
  } catch {
    return null
  }
}
