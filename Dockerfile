# ---- Builder Stage ----
# 此阶段负责安装所有依赖并构建项目
FROM node:20-alpine AS builder

# 设置镜像源并安装 pnpm
RUN npm config set registry https://registry.npmmirror.com && \
    npm install -g pnpm

# 设置工作目录
WORKDIR /app

# 复制 monorepo 配置文件和所有 package.json
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
RUN mkdir -p apps/client apps/server
COPY apps/client/package.json ./apps/client/
COPY apps/server/package.json ./apps/server/

# 使用 pnpm 安装所有依赖 (包括 devDependencies)
RUN pnpm install --frozen-lockfile

# 复制所有源代码
COPY . .

# 构建前端项目
RUN pnpm run build:copy-frontend

# 构建后端项目
RUN pnpm run build:server


# ---- Runner Stage ----
# 此阶段只包含运行应用所需的最小文件集
FROM node:20-alpine AS runner

ARG PACKAGE_SCRIPT=dev

# 设置默认环境，此值可以在 docker run 时通过 -e PACKAGE_SCRIPT=<env> 覆盖



# 设置镜像源并安装 pnpm 和 pm2
RUN npm config set registry https://registry.npmmirror.com && \
    npm install -g pnpm pm2

# 设置工作目录
WORKDIR /app

# 从 builder 阶段复制 monorepo 配置文件和所有 package.json
COPY --from=builder /app/package.json /app/pnpm-lock.yaml /app/pnpm-workspace.yaml ./
RUN mkdir -p apps/client apps/server
COPY --from=builder /app/apps/client/package.json ./apps/client/
COPY --from=builder /app/apps/server/package.json ./apps/server/

# 只安装生产依赖，并忽略所有脚本
RUN pnpm install --prod --frozen-lockfile --ignore-scripts

# 从 builder 阶段复制构建产物和运行时配置文件
COPY --from=builder /app/apps/server/dist ./apps/server/dist
COPY --from=builder /app/apps/server/public ./apps/server/public
COPY --from=builder /app/apps/server/ecosystem.config.cjs ./apps/server/ecosystem.config.cjs

# 复制并标准化所有环境的 .env 文件
# PM2 会根据 --env <name> 参数加载对应的 .env.<name> 文件
COPY --from=builder /app/apps/server/.env.dev ./apps/server/.env.dev
COPY --from=builder /app/apps/server/.env.pre ./apps/server/.env.pre
COPY --from=builder /app/apps/server/.env.prod ./apps/server/.env.prod


ENV PM2_PUBLIC_KEY=xe58svdfrmcogl7
ENV PM2_SECRET_KEY=qtcsu3vvrzqqewo
# 暴露端口
EXPOSE 3000

# 切换到 server 目录并启动应用
# 使用 sh -c 确保 PACKAGE_SCRIPT 环境变量能被正确解析
WORKDIR /app/apps/server
CMD ["sh", "-c", "pm2-runtime start ecosystem.config.cjs --env \"$PACKAGE_SCRIPT\""]